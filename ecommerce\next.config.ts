import type { NextConfig } from "next";
import { MAIN_URL } from "./constant/urls";

// Configuration for image domains

// Try to parse MAIN_URL if available
let mainUrlPattern: {
  protocol: "http" | "https";
  hostname: string;
  port?: string;
  pathname: string;
} | null = null;

try {
  if (MAIN_URL) {
    const url = new URL(MAIN_URL);
    mainUrlPattern = {
      protocol: url.protocol.replace(":", "") as "http" | "https",
      hostname: url.hostname,
      port: url.port || undefined,
      pathname: "/media/**",
    };
  }
} catch (error) {
  console.error("Error parsing MAIN_URL:", error);
}

const nextConfig: NextConfig = {
  /* config options here */
  eslint: {
    ignoreDuringBuilds: true, // Disable linting during builds
  },
  typescript: {
    ignoreBuildErrors: true, // Disable TypeScript checking during build
  },
  // PWA Configuration
  async headers() {
    return [
      {
        source: '/manifest.json',
        headers: [
          {
            key: 'Content-Type',
            value: 'application/manifest+json',
          },
          {
            key: 'Cache-Control',
            value: 'public, max-age=31536000, immutable',
          },
        ],
      },
    ];
  },
  env: {
    GOOGLE_CLIENT_ID: process.env.GOOGLE_CLIENT_ID,
    GOOGLE_CLIENT_SECRET: process.env.GOOGLE_CLIENT_SECRET,
    NEXT_PUBLIC_GOOGLE_CLIENT_ID: process.env.GOOGLE_CLIENT_ID,
    NEXT_PUBLIC_GOOGLE_CLIENT_SECRET: process.env.GOOGLE_CLIENT_SECRET,
  },

  // Add headers configuration for XML files and security
  async headers() {
    return [
      {
        // Security headers for all pages
        source: '/(.*)',
        headers: [
          {
            key: 'Content-Security-Policy',
            value: process.env.NODE_ENV === 'production'
              ? "default-src 'self'; script-src 'self' 'unsafe-eval' 'unsafe-inline' https://accounts.google.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; img-src 'self' data: https: http:; connect-src 'self' https://api-ecom.trio.net.in https://api-ecom.dev.trio.net.in https://api.phonepe.com https://accounts.google.com; font-src 'self' https://fonts.gstatic.com data:; object-src 'none'; base-uri 'self'; form-action 'self';"
              : "default-src 'self' 'unsafe-eval' 'unsafe-inline'; img-src 'self' data: https: http:; connect-src 'self' http://localhost:* https://api-ecom.trio.net.in https://api-ecom.dev.trio.net.in https://api.phonepe.com https://accounts.google.com;"
          },
          {
            key: 'X-Frame-Options',
            value: 'DENY'
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff'
          },
          {
            key: 'Referrer-Policy',
            value: 'strict-origin-when-cross-origin'
          },
          {
            key: 'Permissions-Policy',
            value: 'camera=(), microphone=(), geolocation=()'
          },
          {
            key: 'X-XSS-Protection',
            value: '1; mode=block'
          }
        ]
      },
      {
        // Apply these headers to all XML files
        source: "/:path*.xml",
        headers: [
          {
            key: "Content-Type",
            value: "application/xml",
          },
          {
            key: "Cache-Control",
            value: "public, max-age=3600, stale-while-revalidate=86400",
          },
        ],
      },
      {
        // Specific headers for sitemap.xml
        source: "/sitemap.xml",
        headers: [
          {
            key: "Content-Type",
            value: "application/xml",
          },
          {
            key: "Cache-Control",
            value: "public, max-age=3600, stale-while-revalidate=86400",
          },
        ],
      },
      {
        // Headers for robots.txt
        source: "/robots.txt",
        headers: [
          {
            key: "Content-Type",
            value: "text/plain",
          },
          {
            key: "Cache-Control",
            value: "public, max-age=3600, stale-while-revalidate=86400",
          },
        ],
      },
    ];
  },

  images: {
    remotePatterns: [
      // Add the main URL pattern if available
      ...(mainUrlPattern ? [mainUrlPattern] : []),
      // Always include api-ecom.trio.net.in
      {
        protocol: "http",
        hostname: "api-ecom.trio.net.in",
        pathname: "/media/**",
      },
      {
        protocol: "https",
        hostname: "minio-triumph.trio.net.in",
        pathname: "/media/**",
      },
      {
        protocol: "http",
        hostname: "api-ecom.dev.trio.net.in",
        pathname: "/media/**",
      },
      {
        protocol: "http",
        hostname: "localhost",
        port: "8000",
        pathname: "/media/**",
      },
      // Add placehold.co for fallback images
      {
        protocol: "https",
        hostname: "placehold.co",
        pathname: "/**",
      },
    ],
  },
};

export default nextConfig;
