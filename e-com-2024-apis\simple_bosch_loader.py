#!/usr/bin/env python3
"""
Simple Bosch Products Loader - No ID conflicts

This script safely loads Bosch products by:
1. Clearing existing Bosch products first
2. Using bulk_create to avoid ID sequence issues
3. Processing images separately after products are created
"""

import os
import sys
import json
import requests
from decimal import Decimal
import re

# Setup Django environment
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "backend.settings")

try:
    import django
    django.setup()
except Exception as e:
    print(f"Error setting up Django: {e}")
    sys.exit(1)

from django.db import transaction
from django.core.files.base import ContentFile
from django.utils.text import slugify
from products.models import Category, Brand, Product, ProductImage

class SimpleBoschLoader:
    """Simple loader that avoids ID conflicts"""
    
    def __init__(self):
        self.stats = {'processed': 0, 'created': 0, 'images': 0}
        self.weight_regex = re.compile(r'(\d+\.?\d*)')
        
        # Setup HTTP session
        self.session = requests.Session()
        
        # Caches
        self.category_cache = {}
        self.brand_cache = {}
    
    def extract_weight(self, weight_str):
        """Extract weight from string"""
        if not weight_str:
            return None
        try:
            clean = str(weight_str).lower().replace('kg', '').replace('(excluding battery)', '').strip()
            numbers = self.weight_regex.findall(clean)
            return Decimal(numbers[0]) if numbers else None
        except:
            return None
    
    def get_category(self, name):
        """Get or create category with caching"""
        if name not in self.category_cache:
            category, _ = Category.objects.get_or_create(
                name=name,
                defaults={'description': f'{name} products'}
            )
            self.category_cache[name] = category
        return self.category_cache[name]
    
    def get_brand(self, name):
        """Get or create brand with caching"""
        if name not in self.brand_cache:
            brand, _ = Brand.objects.get_or_create(
                name=name,
                defaults={'description': f'{name} brand'}
            )
            self.brand_cache[name] = brand
        return self.brand_cache[name]
    
    def download_image(self, url):
        """Download image"""
        try:
            response = self.session.get(url, timeout=10)
            response.raise_for_status()
            
            if not response.headers.get('content-type', '').startswith('image/'):
                return None
            
            filename = f"bosch_{hash(url) % 100000}.jpg"
            return ContentFile(response.content, name=filename)
        except:
            return None
    
    def clear_existing_bosch_products(self):
        """Clear existing Bosch products"""
        print("Clearing existing Bosch products...")
        deleted_count = Product.objects.filter(brand__name__icontains='bosch').count()
        Product.objects.filter(brand__name__icontains='bosch').delete()
        print(f"✅ Cleared {deleted_count} existing Bosch products")
    
    def prepare_products(self, products_data):
        """Prepare product objects for bulk creation"""
        print("Preparing products for bulk creation...")
        
        # Pre-create categories and brands
        categories = set(p.get('category', 'Power Tools') for p in products_data)
        brands = set(p.get('brand', 'Bosch') for p in products_data)
        
        for cat_name in categories:
            self.get_category(cat_name)
        for brand_name in brands:
            self.get_brand(brand_name)
        
        # Prepare product objects
        products_to_create = []
        
        for i, product_data in enumerate(products_data):
            try:
                # Get data with fallbacks
                name = product_data.get('name') or f"Bosch Product {i+1}"
                category = self.get_category(product_data.get('category', 'Power Tools'))
                brand = self.get_brand(product_data.get('brand', 'Bosch'))
                weight = self.extract_weight(product_data.get('weight'))
                
                # Handle price
                try:
                    price = Decimal(str(product_data.get('price', 0)))
                    if price < 0:
                        price = Decimal('0')
                except:
                    price = Decimal('0')
                
                # Create product object (don't save yet)
                product = Product(
                    name=name,
                    description=product_data.get('description', ''),
                    category=category,
                    brand=brand,
                    price=price,
                    stock=10,
                    is_active=True,
                    weight=weight
                )
                
                products_to_create.append((product, product_data.get('images', [])))
                
            except Exception as e:
                print(f"Error preparing product {i+1}: {e}")
        
        return products_to_create
    
    def create_products_bulk(self, products_to_create):
        """Create products using bulk operations"""
        print(f"Creating {len(products_to_create)} products...")
        
        with transaction.atomic():
            # Extract just the product objects for bulk_create
            product_objects = [item[0] for item in products_to_create]
            
            # Bulk create products (this will auto-assign IDs)
            created_products = Product.objects.bulk_create(product_objects)
            
            print(f"✅ Created {len(created_products)} products")
            self.stats['created'] = len(created_products)
            self.stats['processed'] = len(created_products)
            
            return created_products, [item[1] for item in products_to_create]
    
    def process_images(self, products, image_lists):
        """Process images for created products"""
        print("Processing images...")
        
        for product, image_urls in zip(products, image_lists):
            if not image_urls:
                continue
            
            try:
                # Download and create images
                for i, url in enumerate(image_urls[:5]):  # Limit to 5 images
                    image_content = self.download_image(url)
                    if image_content:
                        ProductImage.objects.create(
                            product=product,
                            image=image_content,
                            is_primary=(i == 0)
                        )
                        self.stats['images'] += 1
                        
                print(f"Processed images for: {product.name}")
                        
            except Exception as e:
                print(f"Error processing images for {product.name}: {e}")
    
    def load_data(self, file_path):
        """Load and process data"""
        print(f"Loading data from {file_path}...")
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            if 'products' not in data:
                raise ValueError("JSON must contain 'products' key")
            
            products_data = data['products']
            print(f"Found {len(products_data)} products")
            
            # Step 1: Clear existing Bosch products
            self.clear_existing_bosch_products()
            
            # Step 2: Prepare products
            products_to_create = self.prepare_products(products_data)
            
            # Step 3: Bulk create products
            created_products, image_lists = self.create_products_bulk(products_to_create)
            
            # Step 4: Process images
            self.process_images(created_products, image_lists)
            
            # Final stats
            print("\n" + "="*40)
            print("FINAL STATISTICS")
            print("="*40)
            print(f"Expected: {len(products_data)}")
            print(f"Processed: {self.stats['processed']}")
            print(f"Created: {self.stats['created']}")
            print(f"Images: {self.stats['images']}")
            print("="*40)
            
            if self.stats['processed'] == len(products_data):
                print("✅ SUCCESS: All products processed!")
            else:
                print(f"⚠️  WARNING: Expected {len(products_data)}, processed {self.stats['processed']}")
            
            return True
            
        except Exception as e:
            print(f"❌ Error: {e}")
            return False

def main():
    """Main function"""
    file_path = sys.argv[1] if len(sys.argv) > 1 else '../bosch_products_output.json'
    
    if not os.path.exists(file_path):
        print(f"❌ File not found: {file_path}")
        sys.exit(1)
    
    print("🚀 SIMPLE BOSCH LOADER")
    print("="*40)
    
    # Confirm before proceeding
    response = input(f"Load products from {file_path}? (y/N): ")
    if response.lower() != 'y':
        print("Cancelled.")
        sys.exit(0)
    
    # Initialize and run loader
    loader = SimpleBoschLoader()
    success = loader.load_data(file_path)
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
