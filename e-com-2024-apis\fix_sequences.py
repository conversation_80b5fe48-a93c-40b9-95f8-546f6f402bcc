#!/usr/bin/env python3
"""
Fix PostgreSQL sequences for products
"""

import os
import sys

# Setup Django environment
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "backend.settings")

try:
    import django
    django.setup()
except Exception as e:
    print(f"Error setting up Django: {e}")
    sys.exit(1)

from django.db import connection

def fix_sequences():
    """Fix all product-related sequences"""
    cursor = connection.cursor()
    
    # SQL commands to reset sequences
    sql_commands = [
        'SELECT setval(pg_get_serial_sequence(\'"products_product"\',\'id\'), coalesce(max("id"), 1), max("id") IS NOT null) FROM "products_product";',
        'SELECT setval(pg_get_serial_sequence(\'"products_productimage"\',\'id\'), coalesce(max("id"), 1), max("id") IS NOT null) FROM "products_productimage";',
        'SELECT setval(pg_get_serial_sequence(\'"products_category"\',\'id\'), coalesce(max("id"), 1), max("id") IS NOT null) FROM "products_category";',
        'SELECT setval(pg_get_serial_sequence(\'"products_brand"\',\'id\'), coalesce(max("id"), 1), max("id") IS NOT null) FROM "products_brand";'
    ]
    
    for sql in sql_commands:
        try:
            cursor.execute(sql)
            result = cursor.fetchone()
            print(f"✅ Fixed sequence: {result}")
        except Exception as e:
            print(f"❌ Error fixing sequence: {e}")
    
    print("✅ All sequences fixed!")

if __name__ == "__main__":
    fix_sequences()
