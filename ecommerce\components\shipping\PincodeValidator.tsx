'use client';
import React, { useEffect, useState } from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { CheckCircle, XCircle, Loader2, MapPin } from "lucide-react";
import { cn } from "@/lib/utils";
import { usePincodeValidation } from "@/hooks/usePincodeValidation";
import type { PincodeValidatorProps } from "@/types/shipping";
import { Button } from "../ui/button";
import { useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import { usePathname } from "next/navigation";

export const PincodeValidator: React.FC<PincodeValidatorProps> = ({
  value,
  onChange,
  onValidationChange,
  label = "Check Delivery Availability",
  placeholder = "Enter 6-digit pincode",
  className,
}) => {
  const [inputValue, setInputValue] = useState(value);
  const router = useRouter();
  const { status } = useSession();
  const pathName = usePathname();
  const {
    isValid,
    isServiceable,
    isValidating,
    message,
    error,
    validatePincode,
    clearValidation,
  } = usePincodeValidation();

  // Update local input when prop changes
  useEffect(() => {
    setInputValue(value);
  }, [value]);

  // Trigger parent callback when validation changes
  useEffect(() => {
    onValidationChange?.(isValid, isServiceable);
  }, [isValid, isServiceable]);

  const handleValidateClick = () => {
      if (status === "unauthenticated") {
      router.push(`/auth/login?callbackUrl=${encodeURIComponent(pathName)}`);
      return;
    }
    if (inputValue.length === 6) {
      validatePincode(inputValue);
    } else if (inputValue.length === 0) {
      clearValidation();
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value.replace(/\D/g, "").slice(0, 6);
    setInputValue(newValue);
    onChange?.(newValue);
    clearValidation();
  };

  const getValidationIcon = () => {
    if (isValidating)
      return <Loader2 className="h-5 w-5 animate-spin text-gray-400" />;
    if (inputValue.length !== 6)
      return <MapPin className="h-5 w-5 text-gray-400" />;
    if (isValid && isServiceable)
      return <CheckCircle className="h-5 w-5 text-green-500" />;
    if (isValid && !isServiceable)
      return <XCircle className="h-5 w-5 text-orange-500" />;
    // return <XCircle className="h-5 w-5 text-red-500" />;
  };

  const inputBorderClass = () => {
    if (inputValue.length !== 6) return "";
    if (isValid && isServiceable)
      return "border-green-500 focus:border-green-500";
    if (isValid && !isServiceable)
      return "border-orange-500 focus:border-orange-500";
    // return 'border-red-500 focus:border-red-500';
  };

  const getAlertVariant = () => {
    if (error) return "destructive";
    return "default";
  };

  const getAlertClassName = () => {
    if (error) return "border-red-200 bg-red-50";
    if (isValid && isServiceable) return "border-green-200 bg-green-50";
    if (isValid && !isServiceable) return "border-orange-200 bg-orange-50";
    return "border-red-200 bg-red-50";
  };

  const shouldShowAlert = () => {
    return inputValue.length === 6 && (message || error);
  };

  return (
    <div className={cn("space-y-3", className)}>
      <Label htmlFor="pincode" className="text-sm font-medium">
        {label}
      </Label>

      <div className="flex items-center gap-2">
        <div className="relative w-[50%]">
          <Input
            id="pincode"
            type="text"
            value={inputValue}
            onChange={handleInputChange}
            placeholder={placeholder}
            className={cn("transition-colors pr-10 w-full", inputBorderClass())}
            maxLength={6}
            autoComplete="postal-code"
            onKeyDown={handleValidateClick}
          />
          <div className="absolute right-3 top-1/2 -translate-y-1/2">
            {getValidationIcon()}
          </div>
        </div>
        <Button
          size="default"
          variant="outline"
          onClick={handleValidateClick}
          className="whitespace-nowrap"
        >
          Check
        </Button>
      </div>

      {shouldShowAlert() && (
        <Alert
          variant={getAlertVariant()}
          className={cn("py-2 px-3 text-sm", getAlertClassName())}
        >
          <AlertDescription>{error || message}</AlertDescription>
        </Alert>
      )}

      {inputValue.length > 0 && inputValue.length < 6 && (
        <p className="text-xs text-gray-500">
          Enter {6 - inputValue.length} more digit
          {6 - inputValue.length !== 1 ? "s" : ""}
        </p>
      )}
    </div>
  );
};

export default PincodeValidator;
